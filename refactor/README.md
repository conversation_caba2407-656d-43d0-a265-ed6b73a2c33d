# Co-Table 组件重构说明

## 重构概述

本次重构将 `co-table` 和 `page-table` 组件从 Vue Options API 转换为 Composition API，并将两个组件的功能合并为统一的 `co-table` 组件。

## 主要变化

### 1. API 转换
- ✅ 从 Options API 转换为 Composition API (`<script setup>`)
- ✅ 使用纯 JavaScript，无 TypeScript
- ✅ 保持所有 props、events、slots 接口不变

### 2. 功能合并
- ✅ 将 `page-table` 的搜索+表格功能整合到 `co-table` 中
- ✅ 支持两种使用模式：独立表格模式和搜索+表格模式
- ✅ 向后兼容，现有代码无需修改

### 3. 组件架构统一
- ✅ 消除重复代码和组件
- ✅ 统一的事件处理机制
- ✅ 保持与 Element Plus 的完全兼容

## 使用方式

### 独立表格模式（原 co-table 用法）
```vue
<template>
  <co-table 
    :header="tableHeader" 
    :config="tableConfig"
    :data="tableData"
    @operation="handleOperation"
  />
</template>
```

### 搜索+表格模式（原 page-table 用法）
```vue
<template>
  <co-table 
    :header="tableHeader" 
    :config="tableConfig"
    :search-config="searchConfig"
    :form-model="formModel"
    @search="handleSearch"
    @operation="handleOperation"
  />
</template>
```

### 新增 Props

#### searchConfig
- **类型**: `Object`
- **默认值**: `null`
- **说明**: 搜索配置对象，配置后显示搜索区域

#### formModel
- **类型**: `Object`
- **默认值**: `{}`
- **说明**: 搜索表单的数据模型

### 新增 Events

#### search
- **参数**: `{ params, type }`
- **说明**: 搜索事件，当执行搜索或重置时触发

## 迁移指南

### 从 page-table 迁移
原有的 `page-table` 使用方式：
```vue
<page-table
  :table-header="tableHeader"
  :table-config="tableConfig"
  :search-config="searchConfig"
  :form-model="formModel"
  @operation="handleOperation"
  @search="handleSearch"
/>
```

迁移后使用 `co-table`：
```vue
<co-table
  :header="tableHeader"
  :config="tableConfig"
  :search-config="searchConfig"
  :form-model="formModel"
  @operation="handleOperation"
  @search="handleSearch"
/>
```

主要变化：
- `table-header` → `header`
- `table-config` → `config`
- 其他 props 和 events 保持不变

### 从 co-table 迁移
原有的 `co-table` 使用方式完全不变，无需任何修改。

## 文件结构

```
refactor/
├── packages/
│   └── components/
│       └── co-table/
│           ├── components/          # 子组件（已转换为 Composition API）
│           ├── styles/
│           │   └── common.scss
│           ├── utils/
│           │   └── index.js
│           ├── config.js           # 配置文件
│           ├── index.js            # 入口文件
│           ├── search.vue          # 搜索组件（已重构）
│           ├── table.js            # 工具函数
│           └── table.vue           # 主表格组件（已重构）
└── apps/
    └── web-platform/
        └── src/
            └── components/
                └── index.ts        # 更新后的全局注册文件
```

## 兼容性说明

- ✅ 完全向后兼容现有的 `co-table` 用法
- ✅ 支持原有的所有 props、events、slots
- ✅ 保持与 Element Plus 组件库的完全兼容
- ✅ 维持现有的样式和交互行为

## 注意事项

1. **全局注册更新**: 需要使用重构后的 `index.ts` 文件更新全局组件注册
2. **page-table 移除**: `page-table` 组件已被移除，功能已整合到 `co-table` 中
3. **子组件重构**: 所有子组件也已转换为 Composition API
4. **测试建议**: 建议在使用前进行充分的功能测试

## 技术细节

### Composition API 转换要点
- `data()` → `ref()` / `reactive()`
- `computed` → `computed()`
- `watch` → `watch()` / `watchEffect()`
- `methods` → 普通函数
- `mounted/created` → `onMounted()` 等生命周期钩子
- `props/emit` → `defineProps()` / `defineEmits()`

### 组件合并策略
- 通过 `searchConfig` prop 控制是否显示搜索区域
- 统一事件处理机制
- 保持向后兼容的 API 接口
- 整合两个组件的所有功能特性
