<template>
	<el-input v-model.trim="formModel[item.prop]" v-bind="itemAttrs" :placeholder="itemAttrs.placeholder || '请输入' + (itemAttrs.label || '')" v-on="listeners">
		<template v-for="slot in slotList" #[slot.name]>
			<template v-if="typeof item[slot.name] === 'string'">{{ item[slot.name] }}</template>
			<co-select v-else :key="slot.name" v-bind="{ item: slot[slot.name], dic: $attrs.dic, mainProp: slot[slot.name].prop ? '' : item.prop }" :row="formModel" :style="{ minWidth: slot[slot.name].width || '80px' }" @change="onPendChange" />
		</template>
	</el-input>
</template>
<script>
import { handleFn } from './common.js';
import coSelect from './select.vue';
export default {
	name: 'CoInput',
	components: {
		coSelect,
	},
	inheritAttrs: false,
	data() {
		return {
			listeners: {},
		};
	},
	inject: ['widgetItem'],
	created() {
		const {
			listeners,
			$attrs: { item, data = null, row = data, scene },
		} = this;
		this.item = item;
		this.itemAttrs = Object.assign(item.attrs || {}, { clearable: true });
		const _inTable = scene === 'inTable';
		this.formModel = reactive(row);
		// 如果有prepend && 没有其下的prop 不进行字段追加
		// if (!item.prepend && !item.append.prop) {
		if (!this.formModel[item.prop]) {
			this.formModel[item.prop] = '';
		}
		this.widgetItem[item.prop] = this;
		// }
		this.slotList = [];
		// 判断是否有 前置和后置
		item.prepend &&
			this.slotList.push({
				name: 'prepend',
				prepend: item.prepend,
			});
		item.append &&
			this.slotList.push({
				name: 'append',
				append: item.append,
			});
		// 添加默认事件 change
		listeners['change'] = (value) => handleFn.call(this, 'change', value, _inTable, this.$attrs, this.itemAttrs);
		// 为表格内表单时 追加其他事件
		if (_inTable && item.events) {
			for (const evName of Object.keys(item.events)) {
				listeners[evName] = () => handleFn.call(this, evName, row[item.prop], _inTable, this.$attrs, this.itemAttrs);
			}
		}
	},
	methods: {
		resetField(value) {
			this.formModel[this.item.prop] = value;
		},
		onPendChange(data) {
			this.$emit('change', { prop: data.prop, value: data.value });
		},
	},
};
</script>
