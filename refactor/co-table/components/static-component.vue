<script>
import Utils from "../utils";
import { h } from "vue";
export default {
  name: "StaticComponent",
  inheritAttrs: false,
  created() {
    const { item, row, column, handle, index } = this.$attrs;
    const renderByStyles = (styles, data) => {
      const rdData = data !== 0 && !data ? "-" : data;
      return !styles || typeof styles === "string"
        ? h("span", { class: styles }, rdData)
        : h("span", { style: styles }, rdData);
    };
    const formatter =
      item.attrs && item.attrs.formatter ? item.attrs.formatter(row, column, row[item.prop], index) : null;
    const onHandle = (type) => handle({ type, field: item.prop, row, index });
    // 根据类型 进行策略渲染模板
    this.typeTemplate = Object.freeze({
      date: () =>
        h(
          "span",
          {},
          formatter || row[item.prop] ? Utils.parseTime(row[item.prop], item.format || "yyyy-MM-dd HH:mm:ss") : "-"
        ),
      download: () =>
        h(
          "span",
          {
            style: "cursor:pointer",
            onClick: () => onHandle("download"),
          },
          renderByStyles(item.styles, formatter || item.text || "点击下载")
        ),
      preview: () =>
        h(
          "span",
          {
            style: "cursor:pointer",
            onClick: () => onHandle("preview"),
          },
          renderByStyles(item.styles, formatter || item.text || row[item.prop] || "点击查看")
        ),
      img: () =>
        row[item.prop] &&
        h(
          "img",
          {
            src: row[item.prop],
            style: { height: item.height },
            onClick: () => onHandle("preview"),
          },
          {
            placeholder: () => h("div", { class: "zs-flex-center" }, "加载中"),
          }
        ),
      enum: () => {
        const rowProp = row[item.prop];
        if (!rowProp) return h("span", null, "-");
        const itemColor = item.colors && item.colors[rowProp[item["valueKey"] || "value"]];
        const styles =
          itemColor && itemColor.indexOf("#") > -1 ? { style: { color: itemColor } } : { class: itemColor };
        return h("span", { ...styles }, rowProp[item["labelKey"] || "text"]);
      },
      default: () => renderByStyles(item.styles || "", formatter || row[item.prop]),
    });
  },
  render() {
    // 渲染
    const {
      $attrs: { item },
      typeTemplate,
    } = this;
    return item.type && typeTemplate[item.type] ? typeTemplate[item.type]() : typeTemplate["default"]();
  },
};
</script>
