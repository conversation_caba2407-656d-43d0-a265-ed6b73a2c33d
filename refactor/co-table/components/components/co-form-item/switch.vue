<template>
	<div @click.stop>
		<el-switch v-model="formModel[item.prop]" v-bind="item.attrs" :loading="loading" :before-change="onBeforeChange" @change="onChange" />
	</div>
</template>
<script>
import { handleFn } from './common.js';
export default {
	name: 'CoSwitch',
	inheritAttrs: false,
	inject: ['widgetItem'],
	data() {
		return {
			loading: false,
		};
	},
	created() {
		const { item, data = null, row, scene = '' } = this.$attrs;
		this.item = item;
		this.row = data || row;
		this.widgetItem[item.prop] = this;
		this._inTable = scene === 'inTable';
		this.formModel = row;
	},
	methods: {
		resetField(value = undefined) {
			this.formModel[this.item.prop] = value;
		},
		onBeforeChange() {
			const { $attrs, item, row, _inTable } = this;
			const hasBeforeChange = item.attrs && item.attrs['before-change'];
			if (!hasBeforeChange) return true;
			this.loading = true;
			const result = item.attrs['before-change'](row).then((res) => {
				this.loading = false;
				return res;
			});
			handleFn.call(this, 'switch', row[item.prop], _inTable, $attrs, item.attrs);
			return result;
			//  else {
			// 	const rowProp = !row[item.prop];
			// 	handleFn.call(this, 'switch', rowProp, _inTable, this.$attrs, item.attrs);
			// 	this.loading = false;
			// 	return true;
			// }
		},
		onChange(value) {
			const { $attrs, item, _inTable } = this;
			if (!item.attrs || !item.attrs['before-change']) {
				handleFn.call(this, 'switch', value, _inTable, $attrs, item.attrs);
			}
		},
	},
};
</script>
