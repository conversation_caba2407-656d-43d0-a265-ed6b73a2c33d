<template>
	<el-upload
		class="upload-demo"
		v-bind="_attrs"
		action=""
		:multiple="false"
		list-type="text"
		:auto-upload="true"
		:limit="1"
		:drag="false"
		:show-file-list="false"
		:disabled="uploading||_attrs.disabled"
		:before-upload="file=>beforeUpload(file,_attrs)"
		:http-request="httpRequest"
	>
		<el-button v-if="typeof _attrs.styles==='string'" type="text" :[attrName]="_attrs.styles" :loading="uploading">{{ _attrs.text||'上传文件' }}</el-button>
	</el-upload>
</template>

<script>
import defaultConfig from '../../config.js'
import Utils from '../../utils'

export default {
	name: 'CoUpload',
	data() {
		return {
			uploading: false
		}
	},
	computed: {
		_attrs({ $attrs }) {
			return (({ methodFn, linkProps, ...other }) => (other))($attrs)
		}
	},
	created() {
		const attrsStyle = this._attrs.styles
		this.attrName = attrsStyle ? typeof this._attrs.styles === 'string' ? 'class' : 'style' : 'class'
	},
	methods: {
		httpRequest(data) {
			const uploadMethod = defaultConfig.upload
			const uploadFn = this.$attrs.methodFn || uploadMethod
			if (!uploadFn) {
				throw new Error(`upload: global upload and the custom upload at least one`)
			}
			if (!uploadFn || !Utils.getType(uploadFn) === 'Function') {
				throw new Error(`upload: parameter is wrong, should be function`)
			}
			uploadFn(data).then(res => {
				this.$emit('onSuccess', res)
			}).finally(_ => {
				this.uploading = false
			})
		},
		beforeUpload(file, attrs) {
			const extFileName = file.name.substring(file.name.lastIndexOf('.'))
			const uploadFileTypes = attrs.accept.split(',')
			if (uploadFileTypes.length > 0) {
				if (!uploadFileTypes.includes(extFileName)) {
					this.$message.error('不支持的文件类型')
					return false
				}
			}
			const _maxSize = attrs.size || 10; const fileSizeCheckResult = file.size / 1024 / 1024 <= _maxSize
			if (!fileSizeCheckResult) {
				this.$message.error(`已超出文件大小，不能大于(${_maxSize}MB)`)
				return false
			}
			this.uploading = true
			return true
		}
	}
}
</script>
