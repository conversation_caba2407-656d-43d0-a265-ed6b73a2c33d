<template>
  <div
    class="zs-table-page"
    :style="{
      '--highlight-color': highlightColor,
      '--selection-text': `'${selectionText}'`,
      '--header-color': mergeAttrs['header-cell-style']?.color,
    }"
  >
    <!-- 搜索区域 - 整合page-table功能 -->
    <template v-if="searchConfig && searchConfig.items">
      <co-search
        ref="searchRef"
        scene="inTable"
        :dic="dicLoaded ? dicEnumData : null"
        v-model:model="searchFormData"
        :config="searchConfig"
        @search="onSearch"
        @change="(prop, value) => $emit('search-change', prop, value)"
      >
        <template #search_operation="{ handle }">
          <slot name="search_operation" :handle="handle" />
        </template>
        <!-- 透传搜索相关插槽 -->
        <template v-for="(_, slotName) in searchSlots" :key="slotName" #[slotName]="slotProps">
          <slot :name="slotName" v-bind="slotProps || {}" />
        </template>
      </co-search>
    </template>

    <!-- 原有搜索组件兼容 -->
    <template v-else-if="search">
      <co-search
        ref="searchRef"
        scene="inTable"
        :dic="dicLoaded ? dicEnumData : null"
        v-model:model="searchFormData"
        :config="search"
        @search="onSearch"
        @change="(prop, value) => $emit('search-change', prop, value)"
      >
        <template #search_operation="{ handle }">
          <slot name="search_operation" :handle="handle" />
        </template>
      </co-search>
    </template>

    <div v-loading="vloading" class="zs-table-container">
      <div v-if="!!topOperationList.length" class="top-operation">
        <slot name="topOperation" :list="topOperationList">
          <co-button
            v-for="item in topOperationList"
            :key="item.mark"
            :item="item"
            @click="dispatchHandle({ field: item.mark, btn: item, id: _tableKey })"
          />
        </slot>
        <slot name="topOperationText"></slot>
      </div>
      <co-container
        ref="tableForm"
        :model="{ data: tableData }"
        :has-form-item="hasFormItem"
        :config-opts="{ size: $attrs.size || 'default' }"
      >
        <el-table
          :ref="_tableKey + '_elTableRef'"
          :data="tableData"
          :header-cell-class-name="cellClass"
          :row-key="$attrs['row-key'] || '_uuid'"
          :row-class-name="_rowClassName"
          v-bind="mergeAttrs"
          class="zs-table"
        >
          <el-table-column v-if="$slots['expand']" type="expand">
            <template #default="{ row, column, $index }">
              <slot name="expand" v-bind="{ row, column, $index }" />
            </template>
          </el-table-column>
          <template v-slot:empty v-if="$slots['empty']">
            <slot :name="'empty'">暂无数据</slot>
          </template>
          <!-- 列配置中有selection字段 -->
          <template v-if="_selection">
            <el-table-column type="selection" v-bind="_selection" :align="_selection.align || align" />
          </template>
          <template v-for="item in tbHeader">
            <el-table-column v-if="!item.hidden" :key="item.prop" v-bind="item" :align="item.align || align">
              <template v-if="$slots[item.prop + '_header']" #header="slotHeader">
                <slot :name="item.prop + '_header'" v-bind="slotHeader" />
              </template>
              <template v-if="item.type !== 'index'" #default="{ row, column }">
                <template
                  v-if="['input', 'select', 'switch', 'inputNumber'].includes(item.type) || isDateType(item.type)"
                >
                  <slot
                    v-if="$slots[item.prop + '_form-item']"
                    :dicEnum="dicEnum[item.prop]"
                    v-bind="{
                      row,
                      column,
                      $index: row._index,
                      item,
                      prop: item.prop,
                    }"
                    :name="item.prop + '_form-item'"
                  />
                  <el-form-item
                    v-else
                    :key="row._selected"
                    :prop="row._propPath + '.' + item.prop"
                    :rules="row._selected || isValidate ? item?.rules : []"
                    :class="{ 'table-switch-align': align === 'center' }"
                  >
                    <slot
                      :name="item.prop"
                      :dicEnum="dicEnum[item.prop]"
                      v-bind="{
                        row,
                        column,
                        $index: row._index,
                        item,
                        prop: item.prop,
                      }"
                    >
                      <component
                        :is="'co-' + (isDateType(item.type) ? 'date' : item.type)"
                        :type="item.type"
                        scene="inTable"
                        :form-ref="_tableFormRef"
                        :index="row._index"
                        :item="item"
                        :dic="dicEnumData"
                        :row="row"
                        :handle="dispatchHandle"
                      />
                    </slot>
                  </el-form-item>
                </template>
                <template v-else>
                  <slot
                    :name="item.prop"
                    :dicEnum="dicEnum[item.prop]"
                    v-bind="{
                      row,
                      column,
                      $index: row._index,
                      item,
                      prop: item.prop,
                    }"
                  >
                    <template v-if="dicKeyArr.includes(item.prop) && dicLoaded">
                      <span hidden>{{
                        ([propStyle, propData] = [
                          dicEnum[item.prop]["color"] && dicEnum[item.prop]["color"][row[item.prop]],
                          dicEnum[item.prop]["data"][row[item.prop]],
                        ])
                      }}</span>
                      <template v-if="propStyle">
                        <span v-if="propStyle.includes('#')" :style="{ color: propStyle }">{{ propData || "-" }}</span>
                        <span v-else :class="propStyle">{{ propData || "-" }}</span>
                      </template>
                      <span v-else>{{ propData || "-" }}</span>
                    </template>
                    <template v-else-if="item.type === 'upload'">
                      <co-upload v-bind="item" @onSuccess="uploadSuccess($event, row, item, row._index)" />
                    </template>
                    <static-com
                      v-else
                      :item="item"
                      :column="column"
                      :index="row._index"
                      :row="row"
                      :handle="item.type === 'download' ? onDownLoad : dispatchHandle"
                    />
                  </slot>
                </template>
              </template>
            </el-table-column>
          </template>
          <template v-if="!_hiddenOperation">
            <el-table-column
              v-if="operationList.length"
              :fixed="operationOpts.fixed"
              :align="operationOpts.align || align"
              :label="operationOpts.label || '操作'"
              :width="operationOpts.width"
            >
              <template v-if="$slots['operation_header']" #header="slotHeader">
                <slot :name="'operation_header'" v-bind="slotHeader" />
              </template>
              <template #default="{ row }">
                <slot
                  :name="'operation'"
                  :list="inTableRowBtn[currentRowKey + row[currentRowKey]]"
                  :row="row"
                  :index="row._index"
                >
                  <template v-if="Object.keys(showPermisBtn).length > 0">
                    <co-button
                      v-for="item in showPermisBtn[currentRowKey + row[currentRowKey]]"
                      :key="item.mark"
                      :item="item"
                      @click="
                        dispatchHandle({
                          field: item.mark,
                          btn: item,
                          row,
                          index: row._index,
                          id: _tableKey,
                        })
                      "
                    />
                    <el-dropdown
                      v-if="
                        showPermisBtn[currentRowKey + row[currentRowKey]].length !==
                        inTableRowBtn[currentRowKey + row[currentRowKey]].length
                      "
                      :trigger="operationOpts.more.trigger"
                      :hide-on-click="false"
                      style="padding-left: 10px"
                      @command="dispatchHandle"
                    >
                      <co-button :item="operationOpts.more.attrs" dis-click>
                        {{ operationOpts.more.text }}<i class="el-icon-arrow-down el-icon--right" />
                      </co-button>
                      <template v-slot:dropdown>
                        <el-dropdown-menu :style="{ width: operationOpts.more.width }">
                          <el-dropdown-item
                            v-for="item in inTableRowBtn[currentRowKey + row[currentRowKey]].slice(
                              operationOpts.showCount
                            )"
                            :key="item.mark"
                            :command="{
                              field: item.mark,
                              btn: item,
                              row,
                              index: row._index,
                            }"
                          >
                            <co-button :item="item" :dis-click="true" />
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </template>
                </slot>
              </template>
            </el-table-column>
          </template>
          <template #append>
            <slot name="append" />
          </template>
        </el-table>
      </co-container>
      <div v-if="showPagination" class="zs-table-pagination" :style="{ '--page-align': _paging.align }">
        <el-pagination
          v-bind="_paging"
          :page-size="_paging['page-size']"
          :current-page="_paging.current"
          :total="+_paging.total"
          @size-change="handlePaging($event, 'page-size')"
          @current-change="handlePaging($event, 'current')"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, nextTick, useSlots } from 'vue'
import Utils from "./utils/index.js"
import defaultConfig, { dateType } from "./config.js"
import coUpload from "./components/upload/index.vue"
import coButton from "./components/co-button/index.vue"
import coContainer from "./components/co-container/index.vue"
import staticCom from "./components/static-component.vue"
import coFormItem from "./components/co-form-item/index.js"
import coSearch from "./search.vue"
import tableJs from "./table.js"

// 定义组件名称
defineOptions({
  name: 'CoTable',
  inheritAttrs: false
})

// 定义 props
const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
  // 单选模式
  singleMode: {
    type: [Boolean, String],
    default: "",
  },
  // 开启点击row勾选
  currentRow: {
    type: Boolean,
    default: false,
  },
  // 高亮背景色
  highlightColor: {
    type: String,
    default: "",
  },
  // 数据 对齐方式
  align: {
    type: String,
    default: "center",
    validator: (value) => ["left", "center"].includes(value),
  },
  // 表格列配置
  header: {
    type: Array,
    default: () => [],
  },
  // 搜索配置 配置后显示搜索组件 (原有方式)
  search: {
    type: [Boolean, Object],
    default: false,
  },
  // 新增：搜索配置 (page-table方式)
  searchConfig: {
    type: Object,
    default: () => null,
  },
  // 新增：表单模型 (page-table方式)
  formModel: {
    type: Object,
    default: () => ({}),
  },
  // 接口额外参数
  params: {
    type: Object,
    default: () => ({}),
  },
  // 定义是否启用 v-loading 会覆盖全局配置的loading
  loading: {
    type: Boolean,
    default: false,
  },
  // 表格主要配置
  config: {
    type: Object,
    default: () => ({}),
  },
  // 是否验证所有表单
  isValidate: {
    type: Boolean,
    default: false,
  },
})

// 定义 emits
const emit = defineEmits([
  'loaded',
  'data',
  'operation',
  'search-change',
  'selection-change',
  'select',
  'dicLoaded',
  'search'  // 新增：page-table方式的搜索事件
])

// 获取插槽
const slots = useSlots()

// 计算搜索相关插槽
const searchSlots = computed(() => {
  const searchSlotNames = {}
  Object.keys(slots).forEach(slotName => {
    if (slotName.startsWith('search_')) {
      searchSlotNames[slotName] = slots[slotName]
    }
  })
  return searchSlotNames
})

// 响应式数据
const selectionText = ref("")
const searchFormData = ref(props.formModel || props.params || {}) // 支持两种方式
const dataList = ref([])
const tbHeader = ref([])
const currentRowKey = ref("")
const vloading = ref(false)
const operationList = ref([])
const paging = reactive({
  align: "right",
  "page-size": 10,
  current: 1,
  total: 0,
  background: true,
  "page-sizes": [10, 20, 30, 50, 100],
  layout: "total, sizes, prev, pager, next, jumper",
})
const inTableRowBtn = ref({}) // table内所有匹配权限的按钮
const showPermisBtn = ref({}) // table内展示的按钮 从inTableRowBtn中过滤（非折叠按钮)
const selectedData = ref([])
const singleData = ref("") // 单选模式下选中的数据
const dicEnum = ref({})
const dicEnumData = ref({})
const loadDicNum = ref(0)
const showPagination = ref(true) // 默认true：展示分页组件
const dicKeyArr = ref([])

// 内部变量
const _selection = ref(null)
const _tableKey = ref("")
const _tableFormRef = ref(null)
const _childrenKey = ref("children")
const _hiddenOperation = ref(false)
const mergeAttrs = ref({})
const operationOpts = ref({})
const attrsKeys = ref([])
const returnSetFn = ref({})
const searchParamsCache = ref({})
const _hasRequestApi = ref(false)
const elTableRef = ref(null)
const propStyle = ref("")
const propData = ref("")

// refs
const searchRef = ref(null)
const tableForm = ref(null)

// 计算属性
const hasFormItem = computed(() => {
  return !!tbHeader.value.some((item) =>
    ["input", "select", "switch", "textarea", "inputNumber", ...dateType].includes(item.type)
  )
})

// 判断字典是否加载完成
const dicLoaded = computed(() => {
  return loadDicNum.value === dicKeyArr.value.length
})

// 过滤出table顶部按钮
const topOperationList = computed(() => {
  return operationList.value.length
    ? operationList.value.filter((btn) => +btn.inTable === 2 && (btn.tableKey === _tableKey.value || !btn.tableKey))
    : []
})

// 分页数据变化
const _paging = computed(() => {
  const pagination = props.config.pagination
  if (Utils.getType(pagination) === "Object") {
    Object.assign(paging, pagination)
    if (!pagination["page-sizes"] && !paging["page-sizes"].includes(paging["page-size"]))
      paging["page-sizes"].push(paging["page-size"])
    return paging
  }
  return typeof pagination === "boolean" ? pagination : paging
})

const tableData = computed(() => {
  let resData = dataList.value
  if (props.data.length) {
    resData = props.data
    renderOperation()
    if (!resData[0]._uuid) {
      setPrivateKey({ data: resData })
    }
    emitData(resData)
  }
  return resData
})

// 监听器
watch(() => props.header, (n) => {
  // 获取selection列
  _selection.value = (props.config?.header || n).find((item) => item.type === "selection")
  _selection.value && (selectionText.value = _selection.value.label)
  // 表头
  tbHeader.value = _selection.value ? n.filter((item) => !["selection"].includes(item.type)) : n
}, { immediate: true })

// 监听formModel变化 (page-table功能)
watch(() => props.formModel, (newVal) => {
  if (newVal) {
    Object.assign(searchFormData.value, newVal)
  }
}, { deep: true })

// 初始化函数
const init = () => {
  if (_hasRequestApi.value) {
    renderOperation()
    requestData({ model: "" })
  }
}

const emitData = (dataList) => {
  emit('data', dataList)
}

// 设置私有key
const setPrivateKey = ({ data }) => {
  const loopSetKey = ({ target, propPath, index = [] }) => {
    let idx = 0
    for (const row of target) {
      let hasChildren = row[_childrenKey.value] && row[_childrenKey.value].length
      hasFormItem.value && (row._propPath = (propPath === undefined ? `data.` : `${propPath}.children.`) + idx)
      row._uuid = idx + "-" + Utils.uuid()
      if (hasChildren) {
        row._index = index.length ? [...index, idx] : [idx]
        loopSetKey({
          target: row[_childrenKey.value],
          propPath: row._propPath,
          index: row._index,
        })
      } else {
        row._index = idx
      }
      idx += 1
      // 添加权限
      if (!_hiddenOperation.value) {
        operationList.value.length && setPermis(row)
      }
    }
  }
  data.length && loopSetKey({ target: data })
}

// 解析按钮
const renderOperation = () => {
  const operList = props.config?.operation?.list || []
  const operMerge = props.config?.operation?.merge
  const metaPermis = [] // 这里需要根据实际路由获取，暂时为空数组
  let btnList = reactive(operList)
  if (metaPermis.length) {
    if (operMerge) {
      btnList = operMerge === "push" ? [...metaPermis, ...operList] : [...operList, ...metaPermis]
    } else {
      btnList = metaPermis
    }
  }
  if (btnList.length) {
    btnList.forEach((btn) => {
      // 为了兼容 旧系统字段
      "text" in btn && (btn.name = btn.text)
      "showType" in btn && (btn.type = btn.showType)
      "tableName" in btn && (btn.tableKey = btn.tableName)
      "class" in btn && delete btn.class
      delete btn.text
      btn.size = btn.size || "" // 移除$attrs.size依赖
      btn.loading = false
      // 为 element-plus 处理 link text属性
      const btn_attrs = btn.attributes
      if (btn_attrs && typeof btn_attrs === "string") {
        const attrArr = btn_attrs.split(",")
        for (const key of attrArr) {
          btn[key] = true
        }
        delete btn.attributes
      }
    })
    operationList.value = btnList
  }
}

const isDateType = (type) => {
  return dateType.includes(type)
}

// 按钮添加权限
const setPermis = (row) => {
  // 解析并添加权限
  const analysisBtn = operationList.value
    .filter((btn) => +btn.inTable !== 2)
    .filter((btn) => {
      const rule = btn.rule
      rule && rule.includes("storage") && tableJs.matchStorage(rule)
      const _resRule = new Function("row", "storage", "store", `return ${rule || true}`)(row, window.storage, null)
      const resp = _resRule && (!btn.tableKey || _tableKey.value.includes(btn.tableKey))
      const operation = props.config.operation
      // 用于在特定条件下显示隐藏当前行所有操作按钮
      if (operation?.hiddenList) {
        return resp && !operation?.hiddenList(row, _tableKey.value)
      } else {
        return resp
      }
    })
  // 设置 每行要显示的按钮
  inTableRowBtn.value[currentRowKey.value + row[currentRowKey.value]] = Utils.deepClone(analysisBtn)
  // 设置按钮折叠
  const { showCount } = operationOpts.value
  for (const key in inTableRowBtn.value) {
    showPermisBtn.value[key] = showCount < 0 ? inTableRowBtn.value[key] : inTableRowBtn.value[key].slice(0, showCount)
  }
}

// 获取字典
const getRemoteDic = (dicKey, dicBody) => {
  const { getDic } = defaultConfig
  if (!getDic) throw new Error("no getDic methods in the configuration")
  const params =
    typeof dicBody === "string" || typeof dicBody === "function" ? dicBody : dicBody.value || dicBody.data
  let method = typeof dicBody === "function" ? eval(dicBody) : getDic
  method(params)
    .then((res) => transferDic(dicKey, { data: res, color: dicBody.color }))
    .catch(() => init())
}

// 转化 字典 dicConfig
const transferDic = (dicKey, dicBody) => {
  const { data, color, filter = null } = dicBody
  // 记录字典执行数量
  loadDicNum.value++
  // 字典枚举对象 用于模板使用设置内容及样式
  const dicData = filter && typeof filter === "function" ? filter(data) : data || []
  if (dicData.length == 0) {
    console.error(`字典请求异常:${dicKey}`)
  }
  dicEnum.value[dicKey] = {
    data: Object.fromEntries(
      dicData.map((item) => [item[defaultConfig.dicKeys[1]], item[defaultConfig.dicKeys[0]]])
    ),
    color,
  }
  // 字典枚举数据 用于渲染下拉框 如：{status:[{label:'xx',value:'1'}]}
  dicEnumData.value[dicKey] = dicData
  if (dicLoaded.value) {
    // 获取表格数据
    init()
    emit('dicLoaded', dicEnumData.value)
  }
}

// 回调总出口
const dispatchHandle = (params) => {
  emit('operation', params)
}

// 获取接口数据
const requestData = ({ apiName = null, params = null, model = "search" } = {}) => {
  const config = props.config
  // 搜索模式 current重置第一页
  if (model === "search") {
    paging.current = 1
  }
  // 有自定义apiName 将覆盖原来的
  if (apiName) {
    config.request.apiName = apiName
  }
  // 没有配置request 或 apiName 直接终止
  if (!config.request || !config.request.apiName) return
  const {
    apiName: requestApi,
    params: reqParams = {},
    headers = {},
    formatData = null,
    safeNullParams = false,
  } = config.request
  if (!requestApi) return
  const { request, response } = config.page
    ? Utils.deepMerge2(defaultConfig.page, config.page)
    : defaultConfig.page
  const queryData = Object.assign({}, reqParams, searchFormData.value, params)
  // 将空值删除 开启后将过滤保留空的字段
  tableJs.filterNullParams(queryData, safeNullParams, Utils.getType(safeNullParams))
  // // 参数缓存变量 分页使用
  searchParamsCache.value = queryData
  // 是否执行v-loading
  if (props.loading || defaultConfig.loading) {
    vloading.value = true
  }
  // 请求接口
  const apiNameResult = requestApi(
    {
      ...(showPagination.value && {
        [request.size]: paging["page-size"],
        [request.current]: paging.current,
      }),
      ...queryData,
    },
    headers
  )
  // 如果apiNameResult = false 终止以下所有操作(会认为是页面自定义接口)
  if (!apiNameResult) return
  // 执行接口
  return apiNameResult
    .then(async ({ data }) => {
      let responseData = data[response.records]
      responseData = Array.isArray(data) || !responseData ? data : responseData
      const dataIsObject = Utils.getType(responseData) === "Object"
      if ((dataIsObject && !responseData[response.records]) || !responseData) {
        dataList.value.length = 0
        vloading.value = false
        return
      }
      // 当 data 为对象 且没有list或records属性时 终止后面操作 必须通过formData处理数据
      if (dataIsObject) {
        if (formatData) {
          dataList.value = formatData(responseData)
        }
        return
      }
      // 是否 需要处理返回的数据
      if (formatData) {
        // 此处 在各项目中判断不准确，目前建议不要使用async function
        if (["AsyncFunction", "Promise"].includes(Utils.getType(formatData))) {
          responseData = await formatData(responseData)
        } else {
          responseData = formatData(responseData)
        }
      }

      dataList.value = responseData
      paging.total = data[response.total] || responseData.length
      setPrivateKey({ data: responseData })
      emitData(responseData)
      vloading.value = false
    })
    .catch(() => {
      vloading.value = false
    })
}

// 设置请求参数
const setParams = (data = {}) => {
  Object.assign(searchFormData.value, data)
  return returnSetFn.value
}

// 设置row的某一个数据
const setRow = (index, data = null) => {
  const target = dataList.value[index]
  tableJs.setProp.call(null, target, data)
  _tableFormRef.value && _tableFormRef.value.clearValidate()
  return returnSetFn.value
}

// 设置header列的任意属性 当前版本attrs属性除外
const setHeader = (prop, data) => {
  const target = tbHeader.value.find((item) => item.prop === prop)
  tableJs.setProp.call(null, target, data)
  return returnSetFn.value
}

// 设置当前页数据
const setData = (data, type = "") => {
  if (!Array.isArray(data)) throw new Error("data: Must be an Array")
  if (type && ["push", "unshift"].includes(type)) {
    dataList.value[type](...data)
  } else {
    dataList.value = data
  }
  !operationList.value.length && renderOperation()
  emitData(data)
  return returnSetFn.value
}

// 设置分页控件参数
const setPage = (data) => {
  Object.assign(paging, data)
  return returnSetFn.value
}

// 表单验证
const validate = (callback) => {
  let result = true
  if (props.isValidate || _selection.value) {
    _tableFormRef.value.validate((valid) => {
      result = valid
    })
  }
  const res = result ? (selectedData.value.length ? selectedData.value : tableData.value) : result
  if (callback) {
    return callback(res)
  }
  return Promise.resolve(res)
}

// 分页操作
const handlePaging = (value, type) => {
  if (type === "page-size" && paging["current"] > 1) paging["current"] = 1
  paging[type] = value
  if (_hasRequestApi.value) {
    requestData({ params: searchParamsCache.value, model: "" })
  } else {
    emit('operation', { type, value })
  }
}

// 设置 selection 首列隐藏 当开启单选模式时有效
const cellClass = (data) => {
  const columnInfo = data.column
  if (columnInfo.type === "selection") {
    const hasLabel = columnInfo.label
    if (hasLabel) {
      return "selection-header"
    }
  }
  return tableJs.getResultByPros.call(null, "header-cell-class-name", data)
}

// 设置选中背景色
const _rowClassName = (data) => {
  const rowClassName = tableJs.getResultByPros.call(null, "row-class-name", data)
  if (props.highlightColor) {
    const cutrowKey = currentRowKey.value
    for (const key of selectedData.value) {
      if (key[cutrowKey] === data.row[cutrowKey]) {
        return `row-highlight-color${" " + rowClassName}`
      }
    }
    if (attrsKeys.value.includes("highlight-current-row")) {
      return `current-row-color${" " + rowClassName}`
    }
  }
  return rowClassName.trim()
}

// 搜索处理 - 整合page-table功能
const onSearch = (data) => {
  // 原有co-table的搜索处理
  const searchHandle = props.search
  if (searchHandle && typeof searchHandle === 'function') {
    return searchHandle(data)
  }

  // page-table方式的搜索处理
  if (props.searchConfig) {
    // 更新搜索参数
    Object.assign(searchFormData.value, data)
    // 触发搜索事件
    emit('search', { params: data, type: 'search' })
    // 如果有API配置，执行请求
    if (_hasRequestApi.value) {
      requestData({ params: data })
    }
    return
  }

  // 默认处理
  requestData({ params: data })
}

const onDownLoad = (data) => {
  const defaultDownLoad = defaultConfig.download
  defaultDownLoad ? defaultDownLoad(data) : Utils.downFile(data.row[data.field])
}

const uploadSuccess = (data, row, item, index) => {
  const linkProp = item.linkProp
  if (linkProp) {
    const findLinkItem = tbHeader.value.find((item) => item.prop === linkProp)
    const resValue = findLinkItem && findLinkItem.type === "img" ? data.fileUrl : data.fileName
    row[linkProp] ? (row[linkProp] = resValue) : (row[[linkProp]] = resValue)
    return
  }
  dispatchHandle({ type: "upload", data, row, index })
}

// 单选
const singleClick = (val, row, index, from = "") => {
  elTableRef.value.setCurrentRow(row)
  row._selected = !row._selected
  selectedData.value.shift()
  selectedData.value.push(row)
  if (!from) {
    const hasChildren = row[_childrenKey.value] && row[_childrenKey.value].length
    emit('select', {
      row,
      index: hasChildren ? row._index : index,
      tableId: _tableKey.value,
    })
  }
}

// 初始化设置
const initializeComponent = () => {
  const config = props.config
  mergeAttrs.value = { ...defaultConfig.attrs }
  currentRowKey.value = "_uuid" // 简化处理
  attrsKeys.value = []
  // 唯一的tableKey
  _tableKey.value = props.id || ""
  returnSetFn.value = {
    setHeader,
    setRow,
    setData,
    setPage,
    setParams,
  }
  searchParamsCache.value = {}
  // 初始 合并分页数据
  const pagination = config.pagination
  if (typeof pagination === "boolean") {
    showPagination.value = pagination
  } else {
    Object.assign(paging, pagination)
  }
  // 获取树型结构的tree-props
  _childrenKey.value = "children" // 简化处理
  // 操作列配置 将config.operation与默认配置合并
  _hiddenOperation.value = config.operation === false
  const defaultOpts = {
    showCount: -1,
    fixed: false,
    trigger: "hover",
    more: {
      width: 200,
      text: "更多",
      attrs: { type: "text", size: "" },
    },
  }
  operationOpts.value = config.operation ? Utils.deepMerge(defaultOpts, config.operation) : defaultOpts
}

// 生命周期 - onMounted
onMounted(() => {
  const config = props.config
  // 是否有request .apiName属性
  _hasRequestApi.value = config.request && config.request.apiName
  // el-table 自身ref实例
  elTableRef.value = tableForm.value?.$refs[_tableKey.value + "_elTableRef"]
  // 执行loaded回调 {elTable:el-table的ref，getDataList:获取数据的接口方法}
  emit('loaded', {
    elTableRef: elTableRef.value,
    getDataList: requestData,
    ...returnSetFn.value,
  })
  // 获取 table内form ref
  _tableFormRef.value = tableForm.value?.formRef()
  // 初始化表格所有字典 dic:{Object}
  const dicConfig = config.dic
  // dic转为key array
  dicKeyArr.value = dicConfig ? Object.keys(dicConfig) : []
  if (dicKeyArr.value.length) {
    nextTick(() => {
      for (const [dicKey, dicBody] of Object.entries(dicConfig)) {
        // 如果没有data属性 执行自定义data 数据（Array to Object）  否则 请求远程字典
        if (
          typeof dicBody === "function" ||
          typeof dicBody === "string" ||
          (dicBody.value && Utils.getType(dicBody.value) === "String") ||
          (dicBody.data && Utils.getType(dicBody.data) === "String")
        ) {
          getRemoteDic(dicKey, dicBody)
        } else {
          transferDic(dicKey, dicBody)
        }
      }
    })
  } else {
    // 获取表格数据
    init()
  }
})

// 初始化组件
initializeComponent()

// 暴露方法给父组件 (page-table兼容)
defineExpose({
  // 原有co-table方法
  validate,
  setParams,
  setRow,
  setHeader,
  setData,
  setPage,
  requestData,
  // page-table兼容方法
  onSearchHandle: onSearch,
  oldParams: searchFormData
})
</script>

<style lang="scss" scoped>
.zs-table-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  @mixin flex-align($align: center) {
    display: flex;
    justify-content: $align;
    align-items: $align;
  }

  .zs-table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .zs-table-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  &::after {
    content: var(-selection-text);
  }

  .top-operation {
    & + .zs-table,
    & + .zs-table-content {
      margin-top: 10px;
    }
  }

  .zs-flex-center {
    @include flex-align;
  }

  .zs-table :deep(.cell .zs-radio_check .el-radio__label) {
    display: none;
  }

  .zs-table :deep(.cell) {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__error {
      position: initial;
      width: 100%;
      text-align: left;
    }

    .el-date-editor.el-input,
    .el-date-editor.el-input__inner {
      width: 100%;
    }

    .table-switch-align > .el-form-item__content {
      @include flex-align;
    }
  }

  :deep(.row-highlight-color),
  :deep(.current-row.current-row-color) {
    td {
      background-color: var(--highlight-color) !important;
    }
  }

  :deep(.hidden-selection),
  :deep(.has-selection) {
    & > .cell {
      label.el-checkbox {
        display: none;
      }
    }
  }

  :deep(.has-selection > .cell) {
    &::after {
      content: var(--selection-text);
      color: var(--header-color);
    }
  }

  :deep(.selection-header) {
    & > .cell .el-checkbox::after {
      content: var(--selection-text);
      padding-left: 4px;
      color: var(--header-color);
      font-weight: bold;
    }
  }

  .zs-table-pagination {
    padding: 10px 0;
    text-align: var(--page-align);
  }

  :deep(.zs-radio-hook .el-radio__input) {
    & .el-radio__inner {
      width: 17px;
      height: 17px;
    }

    & .el-radio__inner::after {
      content: "";
      box-sizing: content-box;
      border: 1px solid #fff;
      border-left: 0;
      border-top: 0;
      height: 7px;
      left: 5px;
      position: absolute;
      top: 2px;
      transform: rotate(45deg) scaleY(0);
      width: 4px;
      border-radius: 0;
      transition: transform 0.06s ease-in;
      transform-origin: center;
      background: none;
    }

    &.is-checked .el-radio__inner::after {
      transform: rotate(45deg) scaleY(1);
    }
  }
}
</style>
