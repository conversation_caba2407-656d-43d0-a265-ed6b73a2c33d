<template>
  <el-form
    ref="searchFormRef"
    :model="model"
    v-bind="$attrs"
    :inline="$attrs.inline || true"
    :size="$attrs.size || 'default'"
    :validate-on-rule-change="false"
    :[styleName]="_styles"
  >
    <template v-for="item in config.items" :key="item.prop">
      <el-form-item v-if="!item.hidden" :prop="item.prop" v-bind="item.attrs">
        <slot :name="$attrs.scene ? `search_${item.prop}` : item.prop" :item="item" :data="model">
          <component :is="'co-' + getType(item)" :item="item" :data="model" :dic="dic" @change="onItemChange" />
        </slot>
      </el-form-item>
    </template>
    <el-form-item v-if="config.items">
      <slot :name="$attrs.scene ? 'search_operation' : 'operation'" :handle="onHandle">
        <el-button v-bind="searchProps" @click="onHandle('search')">{{ searchProps.name }}</el-button>
        <el-button v-bind="resetProps" @click="onHandle('reset')">{{ resetProps.name }}</el-button>
      </slot>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref, reactive, provide, onMounted } from 'vue'
import defaultConfig, { dateType } from './config.js'
import coFormItem from './components/co-form-item/index.js'
import Utils from './utils'

// 定义组件名称
defineOptions({
  name: 'CoSearch',
  inheritAttrs: false
})

// 定义 props
const props = defineProps({
  model: {
    type: Object,
    default: () => ({}),
  },
  config: {
    type: Object,
    default: () => {},
  },
  dic: {
    type: Object,
    default: () => null,
  },
})

// 定义 emits
const emit = defineEmits(['update:model', 'change', 'search'])

// 响应式数据
const formModel = reactive({})
const widgetItem = reactive({})
const searchFormRef = ref(null)
const _styles = ref(null)
const styleName = ref('class')
const oldModel = ref({})
const searchProps = ref({})
const resetProps = ref({})

// 提供给子组件
provide('widgetItem', widgetItem)

// 方法
const getType = (item) => {
  if (dateType.includes(item.type)) {
    return 'date'
  }
  return item.type
}

const onItemChange = ({ prop, value }) => {
  props.model[prop] = value
  emit('change', { prop, value })
}

const onHandle = (type = 'search') => {
  const formModelArr = Object.keys(props.model)
  const searchResult = {}

  for (const widget of formModelArr) {
    // 重置
    if (type === 'reset') {
      const setVal = oldModel.value ? oldModel.value[widget] : ''
      if (widgetItem[widget]) {
        widgetItem[widget].resetField(setVal)
      }
      props.model[widget] = setVal
    } else {
      // 搜索
      const hasPrepend = widget.includes('_prepend') // 判断是否包含约定的_prepend
      // 如果有_prepend
      if (hasPrepend) {
        // 当前prepend有值
        if (props.model[widget]) {
          const mainProp = widget.replace(/\_prepend$/, '')
          // 如果是时间类型组件
          if (widgetItem[mainProp]?.isDateType) {
            // 是否有分割字段
            const splitProp = widgetItem[mainProp]?.item.splitProp
            // 有分割 将分割字段与item.prop进行拼接，将选择的时间一一赋值，(当有splitProp时 选择时间默认为数组格式)
            if (splitProp && Utils.getType(splitProp) === 'Array') {
              if (props.model[mainProp]?.length) {
                searchResult[`${props.model[widget]}${splitProp[0]}`] = props.model[mainProp][0] ?? ''
                searchResult[`${props.model[widget]}${splitProp[1]}`] = props.model[mainProp][1] ?? ''
              }
            } else {
              //  没有分割 按照选择的prepend字段进行赋值
              searchResult[props.model[widget]] = props.model[mainProp].join(splitProp || ',')
            }
          } else {
            props.model[mainProp] && (searchResult[props.model[widget]] = props.model[mainProp])
          }
        } else {
          delete searchResult[widgetItem[widget].cacheKey]
          widgetItem[widget].cacheKey = undefined
        }
      } else if (!widgetItem[widget]?.item.prepend || !widgetItem[widget]?.item.prepend.prop.includes('_prepend')) {
        searchResult[widget] = props.model[widget]
      }
    }
  }

  emit('search', searchResult, type)
}

const setData = (data) => {
  const formModel = reactive(props.model)
  const formModelArr = Object.keys(formModel)
  for (const [key, value] of Object.entries(data)) {
    if (widgetItem[key]) {
      widgetItem[key].resetField(value)
    }
    if (formModelArr.includes(key)) {
      formModel[key] = value || ''
    } else {
      formModel[key] = value || ''
    }
  }
}

// 初始化
const initializeSearch = () => {
  const styles = props.config?.styles
  _styles.value = styles || defaultConfig.search.style
  styleName.value = typeof _styles.value === 'string' ? 'class' : 'style'
  oldModel.value = { ...props.model }
  searchProps.value = defaultConfig.search.search
  resetProps.value = defaultConfig.search.reset
}

// 生命周期
onMounted(() => {
  // searchFormRef 已经通过 ref 自动绑定
})

// 初始化组件
initializeSearch()

// 暴露方法
defineExpose({
  setData
})
</script>

<style lang="scss">
.el-form--inline {
  .el-form-item {
    .el-input,
    .el-cascader,
    .el-select,
    .el-autocomplete {
      width: 220px;
    }
    .el-date-editor {
      width: 360px;
      &.el-date-editor--datetimerange {
        width: 380px;
      }
      &.el-date-editor--monthrange,
      &.el-date-editor--yearrange {
        width: 270px;
      }
    }
    .el-input-group__prepend,
    .el-input-group__append {
      .el-select {
        width: 120px;
      }
    }
    .el-input:has(.el-input-group__prepend, .el-input-group__append) {
      width: 320px;
    }
    .el-input.el-input-group--append.el-input-group--prepend {
      width: 400px;
    }
  }
}
</style>
