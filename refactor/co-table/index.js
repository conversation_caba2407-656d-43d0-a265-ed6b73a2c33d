import defaultConfig from './config.js';
import Utils from './utils';
import coTable from './table.vue';
import coSearch from './search.vue';
import './styles/common.scss';

window.storage = {};

const install = (Vue, opts = {}) => {
  if (Object.keys(opts).length) {
    Utils.deepMerge(defaultConfig, opts);
  }
  Vue.component(coTable.name, coTable);
  Vue.component(coSearch.name, coSearch);
};

coTable.install = (Vue, opts = {}) => {
  install(Vue, opts);
};

if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue, (opts = {}));
}

export default coTable;
