const tableJs = {
	/**
	 * 设置选中状态 用法同el-table的toggleRowSelection
	 * @param {object} row 目标数据 row
	 * @param {boolean} selected 选中状态 默认：true
	 */
	toggleRowSelection: function (row, selected = true, elToggleRowSelection) {
		const cRowKey = this.currentRowKey;
		const childKey = this._childrenKey;
		if (this.singleMode) {
			this.singleData = row[cRowKey];
			this.selectedData = [row];
		} else {
			const setToggleRow = (row, selected) => {
				row._selected = selected;
				if (selected) {
					!row[childKey] && this.selectedData.push(row);
				} else {
					this.selectedData.splice(
						this.selectedData.findIndex((item) => item[cRowKey] === row[cRowKey]),
						1
					);
				}
				if (row[childKey]) {
					for (let child of row[childKey]) {
						setToggleRow(child, selected);
					}
				}
			};
			setToggleRow(row, selected);
		}
		elToggleRowSelection(row, selected);
	},
	// 动态设置 header参数/row字段
	setProp(target, data) {
		const entriesData = reactive(Object.entries(data));
		for (const [key, value] of entriesData) {
			target[key] ? (target[key] = value) : (target[key] = value);
		}
	},
	getRowIndex(row) {
		const crowKey = this.currentRowKey;
		return row[this._childrenKey] && row[this._childrenKey].length ? row._index : this.tableData.findIndex((item) => item[crowKey] === row[crowKey]);
	},
	// 处理监听事件
	initListeners: function (listeners) {
		if (Object.keys(listeners).length) {
			const tableKey = this._tableKey;
			Object.assign(listeners, {
				'select-all': (selection) => {
					this.tableData.forEach((row) => {
						row._selected = !!selection.length ? !row._selected : false;
					});
					this.selectedData = selection;
					this.$emit('select-all', { selection, tableId: tableKey });
				},
				select: (selection, row) => {
					row._selected = !row._selected;
					this.selectedData = selection; // 赋值给变量 用于修改选中背景色
					this.$emit('select', { selection, row, index: tableJs.getRowIndex.call(this, row), tableId: tableKey });
				},
				'row-click': (row, column, event) => {
					if (column.type === 'selection') return;
					event.stopPropagation();
					// 是否可单行点击
					row._selected = !row._selected;
					const index = tableJs.getRowIndex.call(this, row);
					if (this.currentRow) {
						this.singleMode ? this.singleClick('', row, index, true) : this.elTableRef.toggleRowSelection(row, row._selected);
					}
					this.$emit('row-click', { row, column, event, index, tableId: tableKey });
				},
				'selection-change': (selection) => {
					this.selectedData = selection;
					this.$emit('selection-change', selection);
				},
			});
		}
		return listeners;
	},
	//
	filterNullParams: function (queryData, safeNullParams, paramsType) {
		if (paramsType === 'Boolean' && !safeNullParams) {
			for (let [key, value] of Object.entries(queryData)) {
				(value === '' || value === undefined || value === null) && delete queryData[key];
			}
			return;
		}
		// array 哪些字段受保护 不被删除
		if (paramsType === 'Array') {
			const paramsArr = Object.keys(queryData),
				removeParams = paramsArr.filter((item) => !safeNullParams.includes(item));
			for (let key of removeParams) {
				(queryData[key] === undefined || queryData[key] === null || queryData[key] === '') && delete queryData[key];
			}
		}
	},
	//
	getResultByPros: function (propsName, data) {
		const attrsProp = this.$attrs[propsName];
		return attrsProp ? (typeof attrsProp === 'function' ? attrsProp(data) : attrsProp) : '';
	},
	// 处理按钮 rule中使用localStorage/sessionStorage数据
	matchStorage: (rule) => {
		const matchRes = rule.match(/storage.(\S*)(?=)/);
		const varKeys = matchRes[1].split('.');
		const varKeys0 = varKeys[0];
		if (window.storage[varKeys0]) return;
		const rootKey = sessionStorage[varKeys0] || localStorage[varKeys0];
		if (rootKey) {
			window.storage[varKeys0] = varKeys.length > 1 ? JSON.parse(rootKey) : rootKey;
		}
	},
};

export default tableJs;
