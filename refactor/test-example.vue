<template>
  <div>
    <h2>Co-Table 重构测试示例</h2>
    
    <!-- 独立表格模式测试 -->
    <h3>1. 独立表格模式（原 co-table 用法）</h3>
    <co-table 
      :header="tableHeader" 
      :config="tableConfig"
      :data="tableData"
      @operation="handleOperation"
      @loaded="onTableLoaded"
    />
    
    <hr style="margin: 20px 0;" />
    
    <!-- 搜索+表格模式测试 -->
    <h3>2. 搜索+表格模式（原 page-table 用法）</h3>
    <co-table 
      :header="tableHeader" 
      :config="tableConfig"
      :search-config="searchConfig"
      :form-model="formModel"
      @search="handleSearch"
      @operation="handleOperation"
      @loaded="onTableLoaded"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

// 表格列配置
const tableHeader = ref([
  {
    prop: 'id',
    label: 'ID',
    width: 80
  },
  {
    prop: 'name',
    label: '姓名',
    width: 120
  },
  {
    prop: 'age',
    label: '年龄',
    width: 80
  },
  {
    prop: 'status',
    label: '状态',
    width: 100
  }
])

// 表格数据
const tableData = ref([
  { id: 1, name: '张三', age: 25, status: '正常' },
  { id: 2, name: '李四', age: 30, status: '正常' },
  { id: 3, name: '王五', age: 28, status: '禁用' }
])

// 表格配置
const tableConfig = ref({
  operation: {
    list: [
      {
        name: '编辑',
        mark: 'edit',
        type: 'primary',
        size: 'small',
        inTable: 1
      },
      {
        name: '删除',
        mark: 'delete',
        type: 'danger',
        size: 'small',
        inTable: 1
      }
    ]
  },
  pagination: {
    'page-size': 10,
    current: 1,
    total: 3
  }
})

// 搜索配置
const searchConfig = ref({
  items: [
    {
      prop: 'name',
      label: '姓名',
      type: 'input',
      attrs: {
        placeholder: '请输入姓名'
      }
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      attrs: {
        placeholder: '请选择状态'
      },
      options: [
        { label: '正常', value: '正常' },
        { label: '禁用', value: '禁用' }
      ]
    }
  ]
})

// 搜索表单数据
const formModel = reactive({
  name: '',
  status: ''
})

// 事件处理
const handleOperation = (params) => {
  console.log('操作事件:', params)
  if (params.field === 'edit') {
    console.log('编辑行:', params.row)
  } else if (params.field === 'delete') {
    console.log('删除行:', params.row)
  }
}

const handleSearch = (params) => {
  console.log('搜索事件:', params)
  // 这里可以调用API进行搜索
}

const onTableLoaded = (tableInstance) => {
  console.log('表格加载完成:', tableInstance)
}
</script>

<style scoped>
h2, h3 {
  color: #333;
  margin: 10px 0;
}

hr {
  border: none;
  border-top: 1px solid #eee;
}
</style>
